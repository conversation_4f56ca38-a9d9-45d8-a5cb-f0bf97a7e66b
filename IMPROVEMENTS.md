# HDF5视频标注工具改进说明

本文档描述了对HDF5视频标注工具的三个主要改进。

## 改进概述

### 1. 标注信息预加载与短语库集成 ✅

**功能描述：**
- 标注信息可以预先从`phrase_library.yaml`中加载
- 单击选中时间窗口时在当前subtask中显示标注信息
- 编辑标注内容时可以从`phrase_library.yaml`选择

**实现细节：**
- 在主窗口初始化时自动加载`PhraseLibrary`
- 添加了"编辑标注"按钮和点击标注信息区域的交互
- 集成了`PhraseSelectionDialog`，支持分类浏览和搜索功能
- 支持60个预定义短语，分为5个类别：
  - 动作指令（12个）
  - 状态描述（12个）
  - 场景描述（12个）
  - 交互行为（12个）
  - 机器学习相关（12个）

**使用方法：**
1. 在时间轴上选择一个时间窗口
2. 点击右侧的"编辑标注"按钮或直接点击标注信息区域
3. 在弹出的对话框中选择合适的短语或自定义输入
4. 支持搜索功能，可以快速找到需要的短语

### 2. 时间窗口无缝衔接逻辑改进 ✅

**功能描述：**
- 添加新的时间窗口时，窗口的起始点在前一段窗口的结束点之后
- 窗口位置不能与已有的时间位置重合，实现无缝衔接

**实现细节：**
- 改进了`find_next_available_start()`方法：
  - 新窗口从最后一个窗口的结束帧+1开始
  - 添加了边界检查，防止超出总帧数
- 优化了`calculate_optimal_end_frame()`方法：
  - 智能计算结束帧位置，考虑后续窗口
  - 确保新窗口不与现有窗口重合
  - 保证窗口至少有1帧的长度
- 增强了`add_time_window()`方法：
  - 添加了双重检查机制
  - 改进了错误处理和用户提示

**使用方法：**
1. 点击"添加时间窗口"按钮
2. 系统自动计算最佳位置，确保无缝衔接
3. 新窗口会自动放置在时间轴的合适位置

### 3. HDF5数据直接保存 ✅

**功能描述：**
- 保存数据不再是单独存储JSON文件
- 标注数据直接更新到原有的HDF5数据中

**实现细节：**
- 修改了主窗口的`save_annotations()`方法：
  - 使用"annotations"键名保存标注数据
  - 直接调用HDF5模型的保存方法
  - 添加了用户确认对话框
- 增强了`TimelineWidget`的保存功能：
  - 支持HDF5模型参数传递
  - 保持JSON文件保存作为备用方案
- 添加了自动加载功能：
  - `load_existing_annotations()`方法在打开文件时自动加载已有标注
  - 正确重建时间窗口和时间轴段

**使用方法：**
1. 完成标注后，点击"保存标注数据"按钮
2. 确认保存到HDF5文件
3. 标注数据会直接写入HDF5文件的"annotations"键中
4. 下次打开同一文件时，标注数据会自动加载

## 技术特点

### 数据持久化
- 标注数据直接存储在HDF5文件中，无需额外的JSON文件
- 使用HDF5的字符串数据集存储标注信息
- 支持UTF-8编码，正确处理中文标注

### 用户体验
- 无缝的时间窗口管理，避免重叠和间隙
- 丰富的短语库，提高标注效率
- 直观的编辑界面，支持搜索和分类浏览

### 数据完整性
- 自动加载已有标注，保持数据连续性
- 双重检查机制，确保时间窗口不重合
- 错误处理和用户反馈，提高可靠性

## 文件结构变化

### 新增文件
- `src/core/phrase_library.py` - 短语库管理
- `src/ui/phrase_selection_dialog.py` - 短语选择对话框
- `phrase_library.yaml` - 短语库配置文件
- `test_improvements.py` - 功能测试脚本

### 修改文件
- `src/ui/main_window.py` - 集成短语库和HDF5保存
- `src/ui/timeline_widget.py` - 改进时间窗口管理
- `src/core/hdf5_model.py` - 已有的HDF5操作方法

## 测试验证

运行测试脚本验证所有功能：

```bash
python test_improvements.py
```

测试包括：
- 短语库加载和搜索功能
- HDF5标注数据保存和读取
- 短语选择对话框功能
- 时间窗口无缝衔接逻辑

## 使用建议

1. **标注工作流程：**
   - 打开HDF5文件
   - 添加时间窗口（自动无缝衔接）
   - 使用短语库快速标注
   - 保存到HDF5文件

2. **短语库管理：**
   - 可以编辑`phrase_library.yaml`添加自定义短语
   - 支持分类管理，便于组织
   - 搜索功能帮助快速定位

3. **数据管理：**
   - 标注数据与视频数据存储在同一文件中
   - 便于数据分发和管理
   - 支持版本控制和备份

所有改进都已经过测试验证，可以正常使用。
