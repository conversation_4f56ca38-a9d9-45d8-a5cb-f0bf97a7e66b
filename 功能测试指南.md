# HDF5视频标注工具 - 时间区间编辑功能测试指南

## 测试环境准备

1. 启动应用程序：
   ```bash
   python main.py
   ```

2. 打开一个HDF5文件进行测试

## 功能测试步骤

### 测试1：基本时间窗口创建和选择

1. **创建时间窗口**：
   - 按Enter键进入范围选择模式
   - 使用方向键调整时间范围
   - 再次按Enter确认创建时间窗口

2. **选择时间窗口**：
   - 在时间轴上点击已创建的时间窗口
   - 观察最上方的subtask信息是否正确显示
   - 确认显示格式：`时间窗口: X-Y 帧\n描述: [描述内容或'未标注']`

### 测试2：通过subtask信息面板编辑

1. **点击编辑**：
   - 选中一个时间窗口后，点击subtask信息面板
   - 或点击"编辑标注"按钮
   - 确认弹出编辑对话框

2. **编辑内容**：
   - 在对话框中编辑标注内容
   - 调整开始帧和结束帧数值
   - 观察持续时间是否实时更新

3. **验证功能**：
   - 尝试设置无效的时间区间（开始帧≥结束帧）
   - 确认有错误提示
   - 尝试与其他窗口重合的时间区间
   - 确认有冲突提示

### 测试3：通过时间轴双击编辑

1. **双击编辑**：
   - 在时间轴上双击任意标注段
   - 确认直接打开编辑对话框
   - 对话框应显示当前的时间区间和内容

2. **时间区间修改**：
   - 修改开始帧和结束帧
   - 点击确定保存
   - 观察时间轴上的段是否正确更新

### 测试4：数据同步验证

1. **时间轴同步**：
   - 编辑时间区间后，检查时间轴显示是否同步更新
   - 段的位置和长度应反映新的时间区间

2. **信息面板同步**：
   - 编辑后，subtask信息面板应显示新的时间区间
   - 当前帧在新区间内时，应显示更新后的信息

3. **数据持久化**：
   - 保存标注数据到HDF5文件
   - 重新加载文件，确认修改被正确保存

### 测试5：边界情况测试

1. **时间区间边界**：
   - 测试帧数为0的情况
   - 测试帧数接近总帧数的情况
   - 测试最小时间窗口（1帧）

2. **冲突检测**：
   - 创建多个相邻的时间窗口
   - 尝试扩展一个窗口使其与相邻窗口重合
   - 确认冲突检测正常工作

3. **重置功能**：
   - 修改时间区间后，点击"重置为原始值"按钮
   - 确认时间区间恢复到编辑前的状态

## 预期结果

### 正常功能
- ✅ 时间窗口选择后，subtask信息正确显示
- ✅ 编辑对话框正确显示当前时间区间和内容
- ✅ 时间区间修改后，时间轴同步更新
- ✅ 数据修改正确保存到HDF5文件

### 错误处理
- ✅ 无效时间区间有明确错误提示
- ✅ 时间区间冲突有警告提示
- ✅ 超出范围的帧数有限制提示

### 用户体验
- ✅ 界面响应流畅，无卡顿
- ✅ 操作逻辑清晰，易于理解
- ✅ 错误提示友好，帮助用户纠正操作

## 常见问题排查

1. **subtask信息不更新**：
   - 检查时间窗口是否正确选中
   - 确认当前帧是否在时间窗口范围内

2. **编辑对话框不显示时间控件**：
   - 确认传入了正确的时间区间参数
   - 检查对话框初始化逻辑

3. **时间轴不同步**：
   - 检查时间区间更新逻辑
   - 确认段的创建和删除操作正确

4. **数据不保存**：
   - 确认HDF5文件有写入权限
   - 检查保存逻辑是否正确调用
