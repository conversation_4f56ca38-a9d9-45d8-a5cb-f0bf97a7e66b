# HDF5视频标注工具 - 时间区间编辑功能

## 功能概述

现在在时间轴上选中每一段时间区间的标注时，最上方的当前subtask信息会更新并显示当前选中的区间的标注。编辑标注时不仅能编辑标注annotation的内容，还能调整时间区间的开始和结束帧，并且会同步到下方的时间轴。

## 新增功能

### 1. 时间区间编辑界面

在标注编辑对话框中新增了"时间区间设置"区域，包含：
- **开始帧输入框**：可以调整时间窗口的开始帧
- **结束帧输入框**：可以调整时间窗口的结束帧  
- **持续时间显示**：实时显示当前时间窗口的持续帧数
- **重置按钮**：可以将时间值重置为原始值

### 2. 智能验证机制

- **时间区间有效性检查**：确保开始帧小于结束帧
- **冲突检测**：防止新的时间区间与现有窗口重合
- **自动调整**：当输入无效值时自动调整相关帧数

### 3. 同步更新机制

- **时间轴同步**：时间区间变化会立即反映到时间轴显示
- **信息面板更新**：subtask信息会实时更新显示新的时间区间
- **数据持久化**：修改会保存到HDF5文件中

## 使用方法

### 方法1：通过subtask信息面板编辑

1. 在时间轴上点击选中一个时间窗口
2. 最上方的subtask信息会显示当前选中区间的详情
3. 点击subtask信息面板或点击"编辑标注"按钮
4. 在弹出的对话框中：
   - 编辑标注内容
   - 调整开始帧和结束帧
   - 点击"确定"保存更改

### 方法2：通过时间轴双击编辑

1. 在时间轴上双击任意标注段
2. 直接打开编辑对话框
3. 同样可以编辑内容和时间区间

## 技术实现

### 核心组件

1. **PhraseSelectionDialog** - 增强的标注编辑对话框
   - 新增时间编辑控件
   - 添加验证逻辑
   - 支持时间区间获取

2. **MainWindow** - 主窗口逻辑更新
   - 冲突检测方法
   - 时间区间变化处理
   - 同步更新机制

3. **TimelineWidget** - 时间轴组件增强
   - 支持时间区间更新的段管理
   - 双击编辑功能增强

### 数据流程

```
用户选择时间窗口 → 显示subtask信息 → 编辑对话框 → 验证输入 → 更新数据 → 同步显示
```

## 注意事项

1. **时间区间约束**：新的时间区间不能与现有窗口重合
2. **帧数范围**：帧数必须在有效范围内（0到总帧数-1）
3. **数据一致性**：所有修改都会同步到时间轴和数据存储
4. **用户体验**：提供清晰的错误提示和操作反馈

## 测试建议

1. 创建几个时间窗口进行标注
2. 尝试编辑时间区间，测试冲突检测
3. 验证时间轴显示是否正确同步
4. 检查数据是否正确保存到HDF5文件
