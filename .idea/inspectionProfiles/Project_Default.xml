<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="209">
            <item index="0" class="java.lang.String" itemvalue="transforms3d" />
            <item index="1" class="java.lang.String" itemvalue="protobuf" />
            <item index="2" class="java.lang.String" itemvalue="std-msgs-stubs" />
            <item index="3" class="java.lang.String" itemvalue="mypy" />
            <item index="4" class="java.lang.String" itemvalue="pre-commit" />
            <item index="5" class="java.lang.String" itemvalue="setuptools" />
            <item index="6" class="java.lang.String" itemvalue="torchvision" />
            <item index="7" class="java.lang.String" itemvalue="pycuda" />
            <item index="8" class="java.lang.String" itemvalue="redis" />
            <item index="9" class="java.lang.String" itemvalue="easydict" />
            <item index="10" class="java.lang.String" itemvalue="dvc" />
            <item index="11" class="java.lang.String" itemvalue="dvc-webdav" />
            <item index="12" class="java.lang.String" itemvalue="open3d" />
            <item index="13" class="java.lang.String" itemvalue="tensorflow-gpu" />
            <item index="14" class="java.lang.String" itemvalue="types-all" />
            <item index="15" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="16" class="java.lang.String" itemvalue="loguru" />
            <item index="17" class="java.lang.String" itemvalue="psutil" />
            <item index="18" class="java.lang.String" itemvalue="sensor-msgs-stubs" />
            <item index="19" class="java.lang.String" itemvalue="scikit-image" />
            <item index="20" class="java.lang.String" itemvalue="pygccxml" />
            <item index="21" class="java.lang.String" itemvalue="pyransac3d" />
            <item index="22" class="java.lang.String" itemvalue="python-fcl" />
            <item index="23" class="java.lang.String" itemvalue="numpy" />
            <item index="24" class="java.lang.String" itemvalue="websocket-client" />
            <item index="25" class="java.lang.String" itemvalue="trimesh" />
            <item index="26" class="java.lang.String" itemvalue="func-timeout" />
            <item index="27" class="java.lang.String" itemvalue="sphinx_rtd_theme" />
            <item index="28" class="java.lang.String" itemvalue="pytest-cov" />
            <item index="29" class="java.lang.String" itemvalue="Deprecated" />
            <item index="30" class="java.lang.String" itemvalue="tomli" />
            <item index="31" class="java.lang.String" itemvalue="types-psutil" />
            <item index="32" class="java.lang.String" itemvalue="panda-python" />
            <item index="33" class="java.lang.String" itemvalue="websockets" />
            <item index="34" class="java.lang.String" itemvalue="scipy" />
            <item index="35" class="java.lang.String" itemvalue="urdf-parser-py" />
            <item index="36" class="java.lang.String" itemvalue="opencv-python" />
            <item index="37" class="java.lang.String" itemvalue="pytest" />
            <item index="38" class="java.lang.String" itemvalue="wxPython" />
            <item index="39" class="java.lang.String" itemvalue="readerwriterlock" />
            <item index="40" class="java.lang.String" itemvalue="pyserial" />
            <item index="41" class="java.lang.String" itemvalue="torch" />
            <item index="42" class="java.lang.String" itemvalue="python-memcached" />
            <item index="43" class="java.lang.String" itemvalue="tqdm" />
            <item index="44" class="java.lang.String" itemvalue="sphinx" />
            <item index="45" class="java.lang.String" itemvalue="modbus-tk" />
            <item index="46" class="java.lang.String" itemvalue="pyplusplus" />
            <item index="47" class="java.lang.String" itemvalue="types-tqdm" />
            <item index="48" class="java.lang.String" itemvalue="pin" />
            <item index="49" class="java.lang.String" itemvalue="gunicorn" />
            <item index="50" class="java.lang.String" itemvalue="fastapi" />
            <item index="51" class="java.lang.String" itemvalue="pydantic" />
            <item index="52" class="java.lang.String" itemvalue="requests" />
            <item index="53" class="java.lang.String" itemvalue="uvicorn" />
            <item index="54" class="java.lang.String" itemvalue="aiohttp" />
            <item index="55" class="java.lang.String" itemvalue="aiohttp-sse-client" />
            <item index="56" class="java.lang.String" itemvalue="httpx" />
            <item index="57" class="java.lang.String" itemvalue="numba" />
            <item index="58" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="59" class="java.lang.String" itemvalue="nvidia-cuda-cupti-cu12" />
            <item index="60" class="java.lang.String" itemvalue="nvidia-cuda-cupti-cu11" />
            <item index="61" class="java.lang.String" itemvalue="nvidia-cufft-cu12" />
            <item index="62" class="java.lang.String" itemvalue="executing" />
            <item index="63" class="java.lang.String" itemvalue="gitdb" />
            <item index="64" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="65" class="java.lang.String" itemvalue="qwen-vl-utils" />
            <item index="66" class="java.lang.String" itemvalue="gradio_client" />
            <item index="67" class="java.lang.String" itemvalue="Pygments" />
            <item index="68" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="69" class="java.lang.String" itemvalue="starlette" />
            <item index="70" class="java.lang.String" itemvalue="nvidia-cufft-cu11" />
            <item index="71" class="java.lang.String" itemvalue="shortuuid" />
            <item index="72" class="java.lang.String" itemvalue="nvidia-cuda-runtime-cu12" />
            <item index="73" class="java.lang.String" itemvalue="nvidia-cuda-runtime-cu11" />
            <item index="74" class="java.lang.String" itemvalue="tianshou" />
            <item index="75" class="java.lang.String" itemvalue="torchaudio" />
            <item index="76" class="java.lang.String" itemvalue="jsonschema" />
            <item index="77" class="java.lang.String" itemvalue="GitPython" />
            <item index="78" class="java.lang.String" itemvalue="transformers" />
            <item index="79" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="80" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="81" class="java.lang.String" itemvalue="pydub" />
            <item index="82" class="java.lang.String" itemvalue="pexpect" />
            <item index="83" class="java.lang.String" itemvalue="click" />
            <item index="84" class="java.lang.String" itemvalue="attrs" />
            <item index="85" class="java.lang.String" itemvalue="contourpy" />
            <item index="86" class="java.lang.String" itemvalue="svgwrite" />
            <item index="87" class="java.lang.String" itemvalue="jedi" />
            <item index="88" class="java.lang.String" itemvalue="regex" />
            <item index="89" class="java.lang.String" itemvalue="tensorboard" />
            <item index="90" class="java.lang.String" itemvalue="asttokens" />
            <item index="91" class="java.lang.String" itemvalue="imageio" />
            <item index="92" class="java.lang.String" itemvalue="platformdirs" />
            <item index="93" class="java.lang.String" itemvalue="av" />
            <item index="94" class="java.lang.String" itemvalue="matplotlib" />
            <item index="95" class="java.lang.String" itemvalue="narwhals" />
            <item index="96" class="java.lang.String" itemvalue="httpcore" />
            <item index="97" class="java.lang.String" itemvalue="idna" />
            <item index="98" class="java.lang.String" itemvalue="referencing" />
            <item index="99" class="java.lang.String" itemvalue="decorator" />
            <item index="100" class="java.lang.String" itemvalue="networkx" />
            <item index="101" class="java.lang.String" itemvalue="nvidia-nvjitlink-cu12" />
            <item index="102" class="java.lang.String" itemvalue="smmap" />
            <item index="103" class="java.lang.String" itemvalue="nvidia-cusparse-cu12" />
            <item index="104" class="java.lang.String" itemvalue="py-cpuinfo" />
            <item index="105" class="java.lang.String" itemvalue="nvidia-nccl-cu12" />
            <item index="106" class="java.lang.String" itemvalue="sniffio" />
            <item index="107" class="java.lang.String" itemvalue="nvidia-cusparse-cu11" />
            <item index="108" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="109" class="java.lang.String" itemvalue="nvidia-nccl-cu11" />
            <item index="110" class="java.lang.String" itemvalue="nvidia-cudnn-cu11" />
            <item index="111" class="java.lang.String" itemvalue="stack-data" />
            <item index="112" class="java.lang.String" itemvalue="zipp" />
            <item index="113" class="java.lang.String" itemvalue="mdurl" />
            <item index="114" class="java.lang.String" itemvalue="linkify-it-py" />
            <item index="115" class="java.lang.String" itemvalue="importlib_metadata" />
            <item index="116" class="java.lang.String" itemvalue="markdown2" />
            <item index="117" class="java.lang.String" itemvalue="wavedrom" />
            <item index="118" class="java.lang.String" itemvalue="aiofiles" />
            <item index="119" class="java.lang.String" itemvalue="nvidia-cudnn-cu12" />
            <item index="120" class="java.lang.String" itemvalue="python-multipart" />
            <item index="121" class="java.lang.String" itemvalue="docker-pycreds" />
            <item index="122" class="java.lang.String" itemvalue="pandas" />
            <item index="123" class="java.lang.String" itemvalue="termcolor" />
            <item index="124" class="java.lang.String" itemvalue="latex2mathml" />
            <item index="125" class="java.lang.String" itemvalue="cmake" />
            <item index="126" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="127" class="java.lang.String" itemvalue="tensorboardX" />
            <item index="128" class="java.lang.String" itemvalue="multidict" />
            <item index="129" class="java.lang.String" itemvalue="yarl" />
            <item index="130" class="java.lang.String" itemvalue="pytz" />
            <item index="131" class="java.lang.String" itemvalue="einops" />
            <item index="132" class="java.lang.String" itemvalue="setproctitle" />
            <item index="133" class="java.lang.String" itemvalue="traitlets" />
            <item index="134" class="java.lang.String" itemvalue="absl-py" />
            <item index="135" class="java.lang.String" itemvalue="joblib" />
            <item index="136" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="137" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="138" class="java.lang.String" itemvalue="h11" />
            <item index="139" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="140" class="java.lang.String" itemvalue="frozenlist" />
            <item index="141" class="java.lang.String" itemvalue="fsspec" />
            <item index="142" class="java.lang.String" itemvalue="nvidia-cusolver-cu11" />
            <item index="143" class="java.lang.String" itemvalue="nvidia-cusolver-cu12" />
            <item index="144" class="java.lang.String" itemvalue="nvidia-curand-cu11" />
            <item index="145" class="java.lang.String" itemvalue="nvidia-curand-cu12" />
            <item index="146" class="java.lang.String" itemvalue="semantic-version" />
            <item index="147" class="java.lang.String" itemvalue="filelock" />
            <item index="148" class="java.lang.String" itemvalue="lit" />
            <item index="149" class="java.lang.String" itemvalue="pip" />
            <item index="150" class="java.lang.String" itemvalue="safetensors" />
            <item index="151" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="152" class="java.lang.String" itemvalue="certifi" />
            <item index="153" class="java.lang.String" itemvalue="anyio" />
            <item index="154" class="java.lang.String" itemvalue="accelerate" />
            <item index="155" class="java.lang.String" itemvalue="pyparsing" />
            <item index="156" class="java.lang.String" itemvalue="Markdown" />
            <item index="157" class="java.lang.String" itemvalue="sympy" />
            <item index="158" class="java.lang.String" itemvalue="tokenizers" />
            <item index="159" class="java.lang.String" itemvalue="nvidia-cuda-nvrtc-cu12" />
            <item index="160" class="java.lang.String" itemvalue="triton" />
            <item index="161" class="java.lang.String" itemvalue="nvidia-cuda-nvrtc-cu11" />
            <item index="162" class="java.lang.String" itemvalue="diffusers" />
            <item index="163" class="java.lang.String" itemvalue="h5py" />
            <item index="164" class="java.lang.String" itemvalue="pure_eval" />
            <item index="165" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="166" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="167" class="java.lang.String" itemvalue="orjson" />
            <item index="168" class="java.lang.String" itemvalue="altair" />
            <item index="169" class="java.lang.String" itemvalue="bitsandbytes" />
            <item index="170" class="java.lang.String" itemvalue="fonttools" />
            <item index="171" class="java.lang.String" itemvalue="mdit-py-plugins" />
            <item index="172" class="java.lang.String" itemvalue="ninja" />
            <item index="173" class="java.lang.String" itemvalue="nvidia-nvtx-cu12" />
            <item index="174" class="java.lang.String" itemvalue="nvidia-nvtx-cu11" />
            <item index="175" class="java.lang.String" itemvalue="peft" />
            <item index="176" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="177" class="java.lang.String" itemvalue="gym" />
            <item index="178" class="java.lang.String" itemvalue="uc-micro-py" />
            <item index="179" class="java.lang.String" itemvalue="ffmpy" />
            <item index="180" class="java.lang.String" itemvalue="hjson" />
            <item index="181" class="java.lang.String" itemvalue="gradio" />
            <item index="182" class="java.lang.String" itemvalue="matplotlib-inline" />
            <item index="183" class="java.lang.String" itemvalue="async-timeout" />
            <item index="184" class="java.lang.String" itemvalue="ptyprocess" />
            <item index="185" class="java.lang.String" itemvalue="imageio-ffmpeg" />
            <item index="186" class="java.lang.String" itemvalue="cloudpickle" />
            <item index="187" class="java.lang.String" itemvalue="wcwidth" />
            <item index="188" class="java.lang.String" itemvalue="llvmlite" />
            <item index="189" class="java.lang.String" itemvalue="Jinja2" />
            <item index="190" class="java.lang.String" itemvalue="nvidia-cublas-cu11" />
            <item index="191" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="192" class="java.lang.String" itemvalue="rpds-py" />
            <item index="193" class="java.lang.String" itemvalue="deepspeed" />
            <item index="194" class="java.lang.String" itemvalue="gym-notices" />
            <item index="195" class="java.lang.String" itemvalue="wandb" />
            <item index="196" class="java.lang.String" itemvalue="urllib3" />
            <item index="197" class="java.lang.String" itemvalue="six" />
            <item index="198" class="java.lang.String" itemvalue="timm" />
            <item index="199" class="java.lang.String" itemvalue="prompt_toolkit" />
            <item index="200" class="java.lang.String" itemvalue="parso" />
            <item index="201" class="java.lang.String" itemvalue="wheel" />
            <item index="202" class="java.lang.String" itemvalue="tzdata" />
            <item index="203" class="java.lang.String" itemvalue="ipython" />
            <item index="204" class="java.lang.String" itemvalue="packaging" />
            <item index="205" class="java.lang.String" itemvalue="einops-exts" />
            <item index="206" class="java.lang.String" itemvalue="pillow" />
            <item index="207" class="java.lang.String" itemvalue="grpcio" />
            <item index="208" class="java.lang.String" itemvalue="aiosignal" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>