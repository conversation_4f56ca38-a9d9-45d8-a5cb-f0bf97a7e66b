# HDF5视频标注工具

一个专业的HDF5视频数据可视化与标注工具，专为机器人学习、计算机视觉和行为分析等领域设计。支持高效的时间序列数据标注、智能短语库集成和直接HDF5数据存储。

## ✨ 核心特性

### 🎥 视频数据可视化
- **多窗口图像显示**：自动识别HDF5中的图像数据，支持多个图像流同时显示
- **智能数据解析**：自动处理压缩和非压缩的图像数据
- **实时播放控制**：支持播放/暂停、速度调节和帧精确控制
- **多数据键支持**：同时显示多个数据通道，每个通道独立时间轴

### 📝 智能标注系统
- **时间窗口标注**：精确的时间区间选择和标注功能
- **智能短语库**：预置60个专业术语，支持5大类别快速选择
- **无缝衔接逻辑**：自动计算时间窗口位置，确保标注连续性
- **双击快速编辑**：支持时间轴双击直接编辑标注内容
- **拖拽调整**：支持时间窗口边界拖拽调整和整体移动

### 💾 数据持久化
- **HDF5直接存储**：标注数据直接保存到原HDF5文件，无需额外文件
- **自动加载**：打开文件时自动加载已有标注数据
- **数据完整性**：确保标注数据与视频数据的一致性和完整性

## 🛠️ 环境要求

- **Python**: 3.10+
- **操作系统**: Linux (推荐) / Windows / macOS
- **内存**: 建议4GB以上（取决于HDF5文件大小）

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🚀 快速开始

### 启动应用

```bash
python main.py
```

### 基本工作流程

1. **📁 打开HDF5文件**
   - 点击"打开文件"按钮选择HDF5文件
   - 程序自动解析文件结构并加载已有标注

2. **🖼️ 图像显示设置**
   - 从左侧列表选择图像键，图像将在独立窗口显示
   - 支持多个图像流同时显示和对比

3. **📊 数据通道配置**
   - 选择数据键并点击"添加所选键"添加到时间轴
   - 非零数据段将以不同颜色在时间轴上显示

4. **⏱️ 时间窗口标注**
   - **方法一**：按Enter键进入范围选择模式，用方向键调整范围
   - **方法二**：点击"添加时间窗口"按钮自动创建无缝衔接窗口
   - **方法三**：双击时间轴上的数据段直接编辑

5. **✏️ 标注内容编辑**
   - 点击时间窗口选中后，点击"编辑标注"按钮
   - 从短语库选择预定义术语或自定义输入
   - 支持搜索功能快速找到合适的标注内容

6. **💾 保存标注数据**
   - 点击"保存标注数据"直接保存到HDF5文件
   - 标注数据与视频数据存储在同一文件中

## 🎯 高级功能

### 智能短语库
- **5大类别**：动作指令、状态描述、场景描述、交互行为、机器学习相关
- **60个预定义短语**：覆盖机器人学习和行为分析常用术语
- **搜索功能**：快速定位所需标注内容
- **自定义扩展**：支持添加项目特定的标注术语

### 时间窗口管理
- **智能衔接**：新窗口自动从前一窗口结束帧+1开始
- **拖拽调整**：支持窗口边界拖拽和整体移动
- **冲突检测**：自动防止时间窗口重叠
- **精确编辑**：支持帧级别的精确时间调整

### 数据可视化
- **多通道显示**：同时显示多个数据键的时间序列
- **颜色编码**：不同数据状态用不同颜色标识
- **实时同步**：图像显示与时间轴完全同步
- **播放控制**：支持播放/暂停、速度调节

## 🔧 中文字体支持

程序已优化中文显示，包含以下特性：

- **内置字体**：项目包含文泉驿微米黑字体，确保跨平台中文显示
- **自动检测**：优先使用系统中文字体
- **UTF-8编码**：全面支持中文标注和界面

如遇中文显示问题：
```bash
# 设置环境变量
export LC_ALL=zh_CN.UTF-8

# 或安装系统中文字体
sudo apt-get install fonts-wqy-microhei  # Ubuntu/Debian
```

## 📁 项目结构

```
hdf5_viewer/
├── main.py                          # 🚀 主程序入口
├── requirements.txt                 # 📦 依赖包列表
├── phrase_library.yaml             # 📝 短语库配置文件
├── README.md                        # 📖 项目文档
├── fonts/                           # 🔤 字体文件目录
│   └── wqy-microhei.ttc            # 中文字体文件
└── src/                            # 💻 源代码目录
    ├── core/                       # 🔧 核心功能模块
    │   ├── hdf5_model.py          # HDF5数据模型和操作
    │   └── phrase_library.py      # 短语库管理
    ├── ui/                         # 🎨 用户界面模块
    │   ├── main_window.py         # 主窗口界面
    │   ├── image_window.py        # 图像显示窗口
    │   ├── timeline_widget.py     # 时间轴控件
    │   └── phrase_selection_dialog.py # 短语选择对话框
    └── utils/                      # 🛠️ 工具类
        ├── font_helper.py         # 字体处理工具
        └── hdf5_reader.py         # HDF5文件读取工具
```

## 🔧 开发与扩展

### 自定义短语库
编辑 `phrase_library.yaml` 文件添加项目特定的标注术语：

```yaml
自定义类别:
  - 自定义动作1
  - 自定义动作2
  - 自定义状态描述
```

### 界面定制
- **主界面**：修改 `src/ui/main_window.py`
- **时间轴**：扩展 `src/ui/timeline_widget.py`
- **图像显示**：调整 `src/ui/image_window.py`

### 数据处理扩展
- **HDF5操作**：扩展 `src/core/hdf5_model.py`
- **数据读取**：修改 `src/utils/hdf5_reader.py`

## 🧪 测试与验证

项目包含完整的测试脚本：

```bash
# 测试改进功能
python test_improvements.py

# 测试交互功能
python test_interaction_features.py

# 测试布局优化
python test_layout_optimization.py
```

## 📊 应用场景

- **🤖 机器人学习**：行为序列标注和分析
- **👁️ 计算机视觉**：视频数据集标注
- **🧠 行为分析**：时间序列行为模式识别
- **📈 数据科学**：多模态时间序列数据可视化
- **🔬 科学研究**：实验数据标注和分析

## 🆘 常见问题

### Q: 如何处理大型HDF5文件？
A: 程序支持压缩数据集，建议使用HDF5压缩格式存储大型视频数据。

### Q: 标注数据如何备份？
A: 标注数据直接存储在HDF5文件中，建议定期备份整个HDF5文件。

### Q: 支持哪些图像格式？
A: 支持HDF5中存储的numpy数组格式图像数据，自动处理压缩和非压缩格式。

### Q: 如何批量处理多个文件？
A: 可以通过修改主程序添加批处理功能，或使用脚本调用核心模块。

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- **PyQt5** - 强大的GUI框架
- **HDF5** - 高效的科学数据存储格式
- **文泉驿项目** - 优秀的开源中文字体

---

**⭐ 如果这个项目对您有帮助，请给个Star支持一下！**