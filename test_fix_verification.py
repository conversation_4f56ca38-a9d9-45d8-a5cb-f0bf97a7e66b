#!/usr/bin/env python3
"""
验证修复后的吸附功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.ui.timeline_widget import TimelineWidget, TimelineBar

def test_method_existence():
    """测试方法是否存在于正确的类中"""
    print("=== 测试方法存在性 ===")
    
    # 创建应用程序实例（GUI组件需要）
    app = QApplication([])
    
    # 创建TimelineBar实例
    timeline_bar = TimelineBar()
    
    # 检查方法是否存在
    methods_to_check = [
        'apply_left_boundary_snap',
        'apply_right_boundary_snap', 
        'apply_adjacent_window_snap',
        'find_window_index_by_segment',
        'find_previous_window_index',
        'find_next_window_index',
        'update_timeline_segment_for_window'
    ]
    
    all_methods_exist = True
    
    for method_name in methods_to_check:
        if hasattr(timeline_bar, method_name):
            print(f"✓ {method_name} 方法存在")
        else:
            print(f"✗ {method_name} 方法不存在")
            all_methods_exist = False
    
    if all_methods_exist:
        print("✓ 所有必需的方法都存在于TimelineBar类中")
    else:
        print("✗ 某些方法缺失")
    
    print("=== 方法存在性测试完成 ===\n")
    return all_methods_exist

def test_method_calls():
    """测试方法调用是否正常"""
    print("=== 测试方法调用 ===")
    
    # 创建应用程序实例
    app = QApplication([])
    
    # 创建TimelineWidget和TimelineBar
    timeline_widget = TimelineWidget()
    timeline_widget.set_frame_count(1000)
    
    # 添加一些测试时间窗口
    timeline_widget.time_windows = [
        [50, 100, "窗口1"],
        [150, 200, "窗口2"],
        [300, 350, "窗口3"],
    ]
    
    # 获取第一个时间轴
    if timeline_widget.timelines:
        timeline_bar = timeline_widget.timelines[0]
    else:
        timeline_bar = timeline_widget.add_timeline("test")
    
    # 创建一个模拟的段对象
    class MockSegment:
        def __init__(self, start, end):
            self.start = start
            self.end = end
            self.key = "annotation"
            self.subtask = "窗口2"
    
    mock_segment = MockSegment(150, 200)
    
    try:
        # 测试左边界吸附
        result = timeline_bar.apply_left_boundary_snap(105, mock_segment)
        print(f"✓ apply_left_boundary_snap 调用成功，结果: {result}")
        
        # 测试右边界吸附
        result = timeline_bar.apply_right_boundary_snap(295, mock_segment)
        print(f"✓ apply_right_boundary_snap 调用成功，结果: {result}")
        
        # 测试相邻窗口吸附
        result = timeline_bar.apply_adjacent_window_snap(105, 195, mock_segment)
        print(f"✓ apply_adjacent_window_snap 调用成功，结果: {result}")
        
        print("✓ 所有方法调用都成功")
        return True
        
    except Exception as e:
        print(f"✗ 方法调用失败: {e}")
        return False
    
    finally:
        print("=== 方法调用测试完成 ===\n")

def main():
    print("开始验证修复...\n")
    
    # 测试方法存在性
    methods_exist = test_method_existence()
    
    if methods_exist:
        # 测试方法调用
        methods_work = test_method_calls()
        
        if methods_work:
            print("🎉 修复验证成功！所有功能都正常工作。")
        else:
            print("❌ 修复验证失败：方法调用有问题。")
    else:
        print("❌ 修复验证失败：方法不存在。")

if __name__ == "__main__":
    main()
