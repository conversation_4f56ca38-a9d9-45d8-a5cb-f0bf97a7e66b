#!/usr/bin/env python3
"""
测试新增的吸附功能
1. 新建时间窗口时的当前帧吸附功能
2. 时间窗口拖拽时的相邻窗口吸附功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt
from src.ui.timeline_widget import TimelineWidget

class SnapFeaturesTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("吸附功能测试")
        self.setGeometry(100, 100, 1200, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建说明标签
        info_label = QLabel("""
测试说明：
1. 当前帧吸附功能：点击"添加时间窗口"按钮时，如果红色当前帧线位置未被占用，新窗口的结束帧会自动吸附到当前帧位置
2. 拖拽吸附功能：拖拽时间窗口时，相邻窗口会自动调整以避免漏帧

测试步骤：
1. 使用滑块调整当前帧位置到某个空白区域（如帧200）
2. 点击"添加时间窗口"，观察新窗口是否吸附到当前帧
3. 拖拽已有时间窗口，观察相邻窗口是否自动调整
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc;")
        layout.addWidget(info_label)
        
        # 创建控制按钮
        button_layout = QHBoxLayout()
        
        self.add_window_btn = QPushButton("添加时间窗口")
        self.add_window_btn.clicked.connect(self.add_time_window)
        button_layout.addWidget(self.add_window_btn)
        
        self.reset_btn = QPushButton("重置测试")
        self.reset_btn.clicked.connect(self.reset_test)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 创建时间轴组件
        self.timeline_widget = TimelineWidget()
        self.timeline_widget.set_frame_count(1000)  # 设置总帧数
        layout.addWidget(self.timeline_widget)
        
        # 连接信号
        self.timeline_widget.windowAdded.connect(self.on_window_added)
        
        # 设置初始测试数据
        self.setup_initial_test_data()
        
    def setup_initial_test_data(self):
        """设置初始测试数据"""
        # 添加一些测试时间窗口
        test_windows = [
            [50, 100, "测试窗口1"],
            [150, 200, "测试窗口2"],
            [300, 350, "测试窗口3"],
        ]
        
        for window in test_windows:
            self.timeline_widget.time_windows.append(window)
            self.timeline_widget.create_window_segment(window)
        
        # 设置当前帧到一个空白位置
        self.timeline_widget.set_current_frame(250)
        
        print("初始测试数据设置完成")
        print(f"当前帧位置: {self.timeline_widget.get_current_frame()}")
        print(f"现有时间窗口: {self.timeline_widget.time_windows}")
    
    def add_time_window(self):
        """添加新的时间窗口"""
        print(f"\n=== 测试添加时间窗口 ===")
        print(f"当前帧位置: {self.timeline_widget.get_current_frame()}")
        print(f"添加前的时间窗口: {self.timeline_widget.time_windows}")
        
        self.timeline_widget.add_time_window()
        
        print(f"添加后的时间窗口: {self.timeline_widget.time_windows}")
        print("=== 测试完成 ===\n")
    
    def reset_test(self):
        """重置测试"""
        print("\n=== 重置测试 ===")
        
        # 清除所有时间窗口
        self.timeline_widget.time_windows.clear()
        
        # 清除时间轴上的段
        for timeline in self.timeline_widget.timelines:
            timeline.segments = [seg for seg in timeline.segments if seg.key != "annotation"]
            timeline.update()
        
        # 重新设置初始数据
        self.setup_initial_test_data()
        
        print("测试重置完成")
        print("=== 重置完成 ===\n")
    
    def on_window_added(self, start, end):
        """处理窗口添加事件"""
        print(f"窗口添加事件: {start}-{end}")
        current_frame = self.timeline_widget.get_current_frame()
        if end == current_frame:
            print("✓ 当前帧吸附功能工作正常！")
        else:
            print(f"当前帧吸附功能未触发，当前帧: {current_frame}, 窗口结束帧: {end}")
    
    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 将键盘事件转发给时间轴组件
        if self.timeline_widget:
            self.timeline_widget.keyPressEvent(event)
        super().keyPressEvent(event)

def main():
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = SnapFeaturesTestWindow()
    window.show()
    
    print("吸附功能测试程序启动")
    print("请按照界面说明进行测试")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
