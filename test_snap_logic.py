#!/usr/bin/env python3
"""
测试吸附功能的逻辑（不需要GUI）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_current_frame_snap_logic():
    """测试当前帧吸附逻辑"""
    print("=== 测试当前帧吸附逻辑 ===")
    
    # 模拟时间窗口数据
    time_windows = [
        [50, 100, "窗口1"],
        [150, 200, "窗口2"],
        [300, 350, "窗口3"],
    ]
    
    # 模拟参数
    total_frames = 1000
    default_window_width = 50
    
    def is_frame_in_existing_window(frame, windows):
        """检查指定帧是否在现有时间窗口内"""
        for start, end, _ in windows:
            if start <= frame <= end:
                return True
        return False
    
    def check_window_overlap(start, end, windows):
        """检查新窗口是否与现有窗口重合"""
        for window_start, window_end, _ in windows:
            if not (end < window_start or start > window_end):
                return True
        return False
    
    def calculate_optimal_end_frame_with_current_frame_snap(start_frame, current_frame, windows):
        """计算最优的结束帧位置，优先考虑当前帧吸附功能"""
        
        # 检查当前帧是否在有效范围内且未被现有时间窗口占用
        if (current_frame > start_frame and 
            current_frame < total_frames and 
            not is_frame_in_existing_window(current_frame, windows)):
            
            # 检查使用当前帧作为结束帧是否会与其他窗口冲突
            if not check_window_overlap(start_frame, current_frame, windows):
                print(f"✓ 使用当前帧吸附功能: 结束帧设置为当前帧 {current_frame}")
                return current_frame
            else:
                print(f"✗ 当前帧 {current_frame} 会导致窗口重合，使用默认计算方法")
        else:
            if current_frame <= start_frame:
                print(f"✗ 当前帧 {current_frame} 不在起始帧 {start_frame} 之后，使用默认计算方法")
            elif is_frame_in_existing_window(current_frame, windows):
                print(f"✗ 当前帧 {current_frame} 已被现有时间窗口占用，使用默认计算方法")
        
        # 如果当前帧不适用，使用默认计算方法
        default_end = start_frame + default_window_width - 1
        return min(default_end, total_frames - 1)
    
    # 测试用例
    test_cases = [
        {"start": 360, "current": 400, "expected_snap": True, "desc": "当前帧在空白区域，应该吸附"},
        {"start": 360, "current": 175, "expected_snap": False, "desc": "当前帧在已有窗口内，不应该吸附"},
        {"start": 360, "current": 350, "expected_snap": False, "desc": "当前帧不在起始帧之后，不应该吸附"},
        {"start": 250, "current": 280, "expected_snap": True, "desc": "当前帧在空白区域，应该吸附"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {case['desc']}")
        print(f"起始帧: {case['start']}, 当前帧: {case['current']}")
        
        end_frame = calculate_optimal_end_frame_with_current_frame_snap(
            case['start'], case['current'], time_windows
        )
        
        is_snapped = (end_frame == case['current'])
        expected = case['expected_snap']
        
        if is_snapped == expected:
            print(f"✓ 测试通过: 结束帧 = {end_frame}")
        else:
            print(f"✗ 测试失败: 结束帧 = {end_frame}, 期望吸附: {expected}, 实际吸附: {is_snapped}")
    
    print("\n=== 当前帧吸附逻辑测试完成 ===\n")

def test_adjacent_window_snap_logic():
    """测试相邻窗口吸附逻辑"""
    print("=== 测试相邻窗口吸附逻辑 ===")
    
    # 模拟时间窗口数据
    time_windows = [
        [50, 100, "窗口1"],
        [150, 200, "窗口2"],  # 要拖拽的窗口
        [300, 350, "窗口3"],
    ]
    
    def apply_adjacent_window_snap(new_start, new_end, dragging_index, windows):
        """应用相邻窗口吸附逻辑"""
        snap_threshold = 5
        
        # 找到相邻窗口
        prev_window_index = None
        next_window_index = None
        
        # 按起始帧排序找到前后窗口
        sorted_windows = [(i, w) for i, w in enumerate(windows)]
        sorted_windows.sort(key=lambda x: x[1][0])
        
        for idx, (window_idx, window) in enumerate(sorted_windows):
            if window_idx == dragging_index:
                if idx > 0:
                    prev_window_index = sorted_windows[idx - 1][0]
                if idx < len(sorted_windows) - 1:
                    next_window_index = sorted_windows[idx + 1][0]
                break
        
        adjusted_start = new_start
        adjusted_end = new_end
        
        # 检查与前一个窗口的吸附
        if prev_window_index is not None:
            prev_window = windows[prev_window_index]
            prev_end = prev_window[1]
            
            if abs(adjusted_start - (prev_end + 1)) <= snap_threshold:
                adjusted_start = prev_end + 1
                print(f"✓ 应用前窗口吸附: 起始帧调整为 {adjusted_start}")
        
        # 检查与后一个窗口的吸附
        if next_window_index is not None:
            next_window = windows[next_window_index]
            next_start = next_window[0]
            
            if abs(adjusted_end - (next_start - 1)) <= snap_threshold:
                adjusted_end = next_start - 1
                print(f"✓ 应用后窗口吸附: 结束帧调整为 {adjusted_end}")
        
        return adjusted_start, adjusted_end
    
    # 测试用例
    test_cases = [
        {"new_start": 105, "new_end": 155, "dragging_idx": 1, "desc": "拖拽到接近前窗口，应该吸附"},
        {"new_start": 245, "new_end": 295, "dragging_idx": 1, "desc": "拖拽到接近后窗口，应该吸附"},
        {"new_start": 120, "new_end": 170, "dragging_idx": 1, "desc": "拖拽到中间位置，不应该吸附"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {case['desc']}")
        print(f"原始位置: {case['new_start']}-{case['new_end']}")
        
        adjusted_start, adjusted_end = apply_adjacent_window_snap(
            case['new_start'], case['new_end'], case['dragging_idx'], time_windows
        )
        
        print(f"调整后位置: {adjusted_start}-{adjusted_end}")
        
        if adjusted_start != case['new_start'] or adjusted_end != case['new_end']:
            print("✓ 发生了吸附调整")
        else:
            print("- 没有吸附调整")
    
    print("\n=== 相邻窗口吸附逻辑测试完成 ===\n")

def main():
    print("开始测试吸附功能逻辑...\n")
    
    test_current_frame_snap_logic()
    test_adjacent_window_snap_logic()
    
    print("所有测试完成！")

if __name__ == "__main__":
    main()
