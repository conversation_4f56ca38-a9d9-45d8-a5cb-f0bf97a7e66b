#!/usr/bin/env python3
"""
测试增强的拖拽功能
1. 按住拖拽方式
2. 防重叠的边界调整逻辑
3. 防重叠的整体移动逻辑
4. 100帧吸附阈值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_drag_threshold_logic():
    """测试拖拽阈值逻辑"""
    print("=== 测试拖拽阈值逻辑 ===")
    
    # 模拟拖拽阈值检查
    drag_threshold = 10  # 像素
    
    test_cases = [
        {"start_pos": 100, "current_pos": 105, "should_drag": False, "desc": "移动5像素，不应该开始拖拽"},
        {"start_pos": 100, "current_pos": 115, "should_drag": True, "desc": "移动15像素，应该开始拖拽"},
        {"start_pos": 100, "current_pos": 90, "should_drag": True, "desc": "反向移动15像素，应该开始拖拽"},
        {"start_pos": 100, "current_pos": 95, "should_drag": False, "desc": "反向移动5像素，不应该开始拖拽"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {case['desc']}")
        distance = abs(case['current_pos'] - case['start_pos'])
        should_drag = distance > drag_threshold
        
        if should_drag == case['should_drag']:
            print(f"✓ 测试通过: 距离={distance}, 开始拖拽={should_drag}")
        else:
            print(f"✗ 测试失败: 距离={distance}, 期望开始拖拽={case['should_drag']}, 实际={should_drag}")
    
    print("=== 拖拽阈值逻辑测试完成 ===\n")

def test_anti_overlap_boundary_logic():
    """测试防重叠边界调整逻辑"""
    print("=== 测试防重叠边界调整逻辑 ===")
    
    # 模拟时间窗口数据
    time_windows = [
        [50, 100, "窗口1"],
        [150, 200, "窗口2"],  # 要调整的窗口
        [300, 350, "窗口3"],
    ]
    
    def apply_left_boundary_anti_overlap(new_start, current_window_idx, windows):
        """模拟防重叠的左边界调整"""
        snap_threshold = 100
        
        # 找到前一个窗口
        prev_window_idx = None
        current_start = windows[current_window_idx][0]
        
        for i, (start, end, _) in enumerate(windows):
            if i != current_window_idx and start < current_start:
                if prev_window_idx is None or start > windows[prev_window_idx][0]:
                    prev_window_idx = i
        
        if prev_window_idx is not None:
            prev_end = windows[prev_window_idx][1]
            
            # 检查重叠
            if new_start <= prev_end:
                new_prev_end = new_start - 1
                if new_prev_end >= windows[prev_window_idx][0]:
                    windows[prev_window_idx] = [windows[prev_window_idx][0], new_prev_end, windows[prev_window_idx][2]]
                    print(f"防重叠: 调整前窗口结束帧为 {new_prev_end}")
                else:
                    new_start = windows[prev_window_idx][0] + 1
                    print(f"防重叠: 限制当前窗口起始帧为 {new_start}")
            
            # 检查吸附
            elif abs(new_start - (prev_end + 1)) <= snap_threshold:
                new_start = prev_end + 1
                print(f"应用左边界吸附: 起始帧调整为 {new_start}")
        
        return new_start
    
    # 测试用例
    test_cases = [
        {"new_start": 95, "desc": "调整到会重叠的位置"},
        {"new_start": 105, "desc": "调整到吸附范围内"},
        {"new_start": 120, "desc": "调整到正常位置"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {case['desc']}")
        windows_copy = [w[:] for w in time_windows]  # 深拷贝
        print(f"原始窗口: {windows_copy}")
        
        result = apply_left_boundary_anti_overlap(case['new_start'], 1, windows_copy)
        print(f"调整后起始帧: {result}")
        print(f"调整后窗口: {windows_copy}")
    
    print("=== 防重叠边界调整逻辑测试完成 ===\n")

def test_anti_overlap_move_logic():
    """测试防重叠整体移动逻辑"""
    print("=== 测试防重叠整体移动逻辑 ===")
    
    # 模拟时间窗口数据
    time_windows = [
        [50, 100, "窗口1"],
        [150, 200, "窗口2"],  # 要移动的窗口
        [300, 350, "窗口3"],
    ]
    
    def apply_anti_overlap_move(new_start, new_end, current_window_idx, windows):
        """模拟防重叠的整体移动"""
        snap_threshold = 100
        
        # 找到前后窗口
        prev_window_idx = None
        next_window_idx = None
        current_start = windows[current_window_idx][0]
        
        for i, (start, end, _) in enumerate(windows):
            if i != current_window_idx:
                if start < current_start and (prev_window_idx is None or start > windows[prev_window_idx][0]):
                    prev_window_idx = i
                elif start > current_start and (next_window_idx is None or start < windows[next_window_idx][0]):
                    next_window_idx = i
        
        adjusted_start = new_start
        adjusted_end = new_end
        
        # 检查与前窗口的重叠
        if prev_window_idx is not None:
            prev_end = windows[prev_window_idx][1]
            if adjusted_start <= prev_end:
                new_prev_end = adjusted_start - 1
                if new_prev_end >= windows[prev_window_idx][0]:
                    windows[prev_window_idx] = [windows[prev_window_idx][0], new_prev_end, windows[prev_window_idx][2]]
                    print(f"防重叠: 调整前窗口结束帧为 {new_prev_end}")
                else:
                    window_length = adjusted_end - adjusted_start
                    adjusted_start = windows[prev_window_idx][0] + 1
                    adjusted_end = adjusted_start + window_length
                    print(f"防重叠: 调整当前窗口位置为 {adjusted_start}-{adjusted_end}")
            elif abs(adjusted_start - (prev_end + 1)) <= snap_threshold:
                adjusted_start = prev_end + 1
                adjusted_end = adjusted_start + (new_end - new_start)
                print(f"应用前窗口吸附: 起始帧调整为 {adjusted_start}")
        
        # 检查与后窗口的重叠
        if next_window_idx is not None:
            next_start = windows[next_window_idx][0]
            if adjusted_end >= next_start:
                new_next_start = adjusted_end + 1
                if new_next_start <= windows[next_window_idx][1]:
                    windows[next_window_idx] = [new_next_start, windows[next_window_idx][1], windows[next_window_idx][2]]
                    print(f"防重叠: 调整后窗口起始帧为 {new_next_start}")
                else:
                    window_length = adjusted_end - adjusted_start
                    adjusted_end = windows[next_window_idx][1] - 1
                    adjusted_start = adjusted_end - window_length
                    print(f"防重叠: 调整当前窗口位置为 {adjusted_start}-{adjusted_end}")
            elif abs(adjusted_end - (next_start - 1)) <= snap_threshold:
                adjusted_end = next_start - 1
                adjusted_start = adjusted_end - (new_end - new_start)
                print(f"应用后窗口吸附: 结束帧调整为 {adjusted_end}")
        
        return adjusted_start, adjusted_end
    
    # 测试用例
    test_cases = [
        {"new_start": 90, "new_end": 140, "desc": "移动到会与前窗口重叠"},
        {"new_start": 250, "new_end": 310, "desc": "移动到会与后窗口重叠"},
        {"new_start": 105, "new_end": 155, "desc": "移动到吸附范围内"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {case['desc']}")
        windows_copy = [w[:] for w in time_windows]  # 深拷贝
        print(f"原始窗口: {windows_copy}")
        
        result_start, result_end = apply_anti_overlap_move(case['new_start'], case['new_end'], 1, windows_copy)
        print(f"调整后位置: {result_start}-{result_end}")
        print(f"调整后窗口: {windows_copy}")
    
    print("=== 防重叠整体移动逻辑测试完成 ===\n")

def main():
    print("开始测试增强的拖拽功能...\n")
    
    test_drag_threshold_logic()
    test_anti_overlap_boundary_logic()
    test_anti_overlap_move_logic()
    
    print("所有测试完成！")

if __name__ == "__main__":
    main()
