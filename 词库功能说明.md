# HDF5可视化工具 - 词库功能说明

## 功能概述

新增的词库功能可以大幅提高语言标注的效率，通过预设的短语库，用户可以快速选择常用的标注内容，而不需要每次手动输入。

## 主要特性

### 1. 增强版输入对话框
- **双模式输入**: 支持手动输入和词库选择两种方式
- **实时预览**: 显示当前选择的内容
- **智能搜索**: 支持关键词搜索过滤短语
- **分类浏览**: 按分类组织短语，便于查找
- **一键使用**: 双击短语即可应用

### 2. 词库管理
- **分类管理**: 支持创建、重命名、删除分类
- **短语管理**: 支持添加、编辑、删除短语
- **即时添加**: 在输入过程中可将新短语添加到词库
- **持久保存**: 词库内容自动保存到YAML文件

### 3. 全键支持
- 适用于所有language键（language、language_new等）
- 保持原有功能完全兼容
- 统一的用户体验

## 使用方法

### 基本使用流程

1. **打开输入对话框**
   - 通过时间轴选择范围或点击段来设置language
   - 系统会自动打开增强版输入对话框

2. **选择输入方式**
   - **手动输入**: 在左侧文本框直接输入描述
   - **词库选择**: 在右侧从预设短语中选择

3. **使用词库选择**
   - 选择分类查看特定类型的短语
   - 使用搜索框快速定位短语
   - 双击短语或点击"使用选中短语"按钮
   - 选中的短语会自动填充到输入框

4. **确认和保存**
   - 检查预览区域的内容
   - 点击"确定"应用更改

### 词库管理

1. **访问词库管理器**
   - 在输入对话框中点击"管理词库"按钮

2. **添加新分类**
   - 在右侧编辑区域输入分类名称
   - 点击"添加分类"按钮

3. **添加新短语**
   - 选择目标分类
   - 输入短语内容
   - 点击"添加短语"按钮

4. **编辑现有内容**
   - 在左侧树形列表中选择要编辑的项目
   - 使用右键菜单进行编辑操作

### 快捷操作

- **Ctrl+Enter**: 在手动输入框中快速确认
- **双击短语**: 直接使用选中的短语
- **搜索过滤**: 实时过滤显示符合条件的短语

## 词库文件格式

词库使用YAML格式存储，结构如下：

```yaml
分类名称1:
  - 短语1
  - 短语2
  - 短语3

分类名称2:
  - 短语A
  - 短语B
  - 短语C
```

文件位置：项目根目录下的`phrase_library.yaml`

## 自定义词库

用户可以通过以下方式自定义词库：

1. **通过界面管理**: 使用词库管理器进行可视化编辑
2. **直接编辑文件**: 修改`phrase_library.yaml`文件后重新加载
3. **运行时添加**: 在输入过程中将新内容添加到词库

## 注意事项

- 词库文件会在首次运行时自动创建默认内容
- 修改词库后需要点击"保存词库"或系统会自动保存
- 删除分类或短语的操作不可逆，请谨慎操作
- 搜索功能支持中文关键词匹配

## 效率提升

使用词库功能可以显著提高标注效率：

- **减少输入时间**: 预设短语一键选择
- **标准化标注**: 统一的术语表达
- **减少错误**: 避免拼写错误和不一致
- **快速定位**: 分类和搜索功能快速找到所需内容

## 技术特性

- **响应式界面**: 支持窗口大小调整
- **内存优化**: 词库按需加载，性能优良
- **兼容性好**: 完全兼容现有的language标注功能
- **可扩展性**: 支持未来添加更多词库管理功能 